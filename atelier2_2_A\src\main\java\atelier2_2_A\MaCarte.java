package atelier2_2_A;

import common.models.enums.Sorte;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;
import javafx.scene.paint.Paint;
import shift3_procedure.models.values.CarteIncomplete;

public class MaCarte extends CarteIncomplete {
    public MaCarte() {
        super();
    }

    public MaCarte(int numero, Sorte sorte) {
        super(numero, sorte);
    }

    @Override
    protected void dessinerCarte(GraphicsContext gc) {
        Sorte sorte = getSorte();

        Color couleur;
        
        if (sorte == Sorte.COEUR) {
            couleur = Color.web("#ff0000");
        } else if (sorte == Sorte.CARREAU) {
            couleur = Color.web("#0000ff");
        } else if (sorte == Sorte.PIQUE) {
            couleur = Color.web("#000000");
        } else if (sorte == Sorte.TREFLE) {
            couleur = Color.web("#90ee90");
        } else {
            couleur = Color.web("#d3d3d3");
        }

        gc.setFill(Color.web("#f5f5dc"));
        gc.fillRect(0, 0, 50, 75);

        gc.setFill(couleur);

        gc.setStroke(couleur);
        gc.strokeRect(0, 0, 50, 75);

        gc.strokeText(String.valueOf(getNumero()) + " " + sorte.getSymbol(), 12.0, 20.0);
        gc.fillText(String.valueOf(getNumero()) + " " + sorte.getSymbol(), 12.0, 20.0);
    }

}
