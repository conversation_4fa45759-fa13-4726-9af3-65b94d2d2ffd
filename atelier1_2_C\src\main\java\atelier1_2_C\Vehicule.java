package atelier1_2_C;

public class Vehicule implements R<PERSON><PERSON>ur, Formateur {
    private double totalKilometres = 0;
    
    protected double consommationLitresParKilometre() {
		return 0.0;
	}

	protected String nomVehicule() {
		return "";
	}

	protected boolean siNomFeminin() {
		return false;
	}

	private double litresEssenceConsomes() {
		return totalKilometres * consommationLitresParKilometre();
	}

	@Override
	public String formater() {
		StringBuilder builder = new StringBuilder();

		if(siNomFeminin()) {
			builder.append("Ma ");
		}else {
			builder.append("Mon ");
		}
		builder.append(nomVehicule());
		builder.append(" a roulé ");
		builder.append(totalKilometres);
		builder.append(" kilomètres et consomé ");
		builder.append(litresEssenceConsomes());
		builder.append(" litres d'essence.");

		return builder.toString();
	}

    @Override
    public void rouler(double kilometres) {
        totalKilometres += kilometres;
    }

}
