package atelier1_2_E;

public class Auto extends Vehicule {
    public Auto(double totalKilometres) {
        super(totalKilometres);
    }

    @Override
	protected double consommationLitresParKilometre() {
		return 8.0;
	}

	@Override
	protected boolean siNomFeminin() {
		return false;
	}

	@Override
	protected String nomVehicule() {
		return "auto";
	}

	@Override
	public String formater() {
		return super.formater() + " J'adore mon " + nomVehicule() + "!";
	}
}
