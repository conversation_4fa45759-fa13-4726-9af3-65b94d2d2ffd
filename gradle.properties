ntroDependencies=ntro_core,ntro_app_fx
cardsDependencies=common,common_procedure
c6Dependencies=3c6_core,3c6_examples,3c6_java,3c6_nitrite,3c6_swing,3c6_tutoriels

atelier1_1Dependencies=freesort_procedure

atelier1_2_ADependencies=atelier1_2_A
atelier1_2_BDependencies=atelier1_2_B
atelier1_2_CDependencies=atelier1_2_C
atelier1_2_DDependencies=atelier1_2_D
atelier1_2_EDependencies=atelier1_2_E

atelier1_3Dependencies=shift_procedure

atelier2_1_ADependencies=shift2_procedure
atelier2_1_BDependencies=validator_common,validator_shift2

atelier2_2_ADependencies=shift3_procedure
atelier2_2_BDependencies=validator_common,validator_shift3

atelier2_3Dependencies=fibonacci_procedure

examen2Dependencies=validator_common,validator_examen2

atelier3_1_ADependencies=atelier3_1_A
atelier3_1_BDependencies=atelier3_1_B

atelier3_2_ADependencies=atelier3_2_A
atelier3_2_BDependencies=atelier3_2_B
atelier3_2_CDependencies=atelier3_2_C

atelier3_3_ADependencies=atelier3_3_A
atelier3_3_BDependencies=atelier3_3_B
atelier3_3_CDependencies=atelier3_3_C

atelier4_1Dependencies=atelier4_1
atelier4_2Dependencies=atelier4_2
atelier4_3_ADependencies=atelier4_3_A
atelier4_3_BDependencies=atelier4_3_B

atelier5_1Dependencies=atelier5_1
atelier5_2Dependencies=atelier5_2
atelier5_3_ADependencies=atelier5_3_A
atelier5_3_BDependencies=atelier5_3_B

version=0.1
