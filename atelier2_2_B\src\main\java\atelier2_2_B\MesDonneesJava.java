package atelier2_2_B;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import validator_shift3.models.DonneesJavaSolutionSuperClass;
import validator_shift3.models.NtroHashMap;

public class MesDonneesJava extends DonneesJavaSolutionSuperClass {
    public MesDonneesJava() {
        super();
    }

    @Override
    public void initialize(String id) {

        if (id.equals("ex01")) {
            List<Object> listeAnimaux = new ArrayList<>();
            this.racine.put("animaux", listeAnimaux);

            Map<String, String> animal1 = new NtroHashMap<>();
            animal1.put("race", "dalmatien");
            animal1.put("espece", "chien");
            animal1.put("typeDeCri", "jappement");
            listeAnimaux.add(animal1);

            Map<String, String> animal2 = new NtroHashMap<>();
            animal2.put("race", "persan");
            animal2.put("espece", "chat");
            animal2.put("typeDeCri", "miaulement");
            listeAnimaux.add(animal2);

            Map<String, String> animal3 = new NtroHashMap<>();
            animal3.put("race", "bouledogue");
            animal3.put("espece", "chien");
            animal3.put("typeDeCri", "jappement");
            listeAnimaux.add(animal3);
        } else if (id.equals("ex02")) {
            List<Object> listeEnfants = new ArrayList<>();
            this.racine.put("enfants", listeEnfants);

            Map<String, Object> parentCarmella = new NtroHashMap<>();
            parentCarmella.put("nom", "Carmella");

            Map<String, Object> parentVito = new NtroHashMap<>();
            parentVito.put("nom", "Vito");

            Map<String, Object> enfant1 = new NtroHashMap<>();
            enfant1.put("nom", "Sonny");
            enfant1.put("mere", parentCarmella);
            enfant1.put("pere", parentVito);
            listeEnfants.add(enfant1);

            Map<String, Object> enfant2 = new NtroHashMap<>();
            enfant2.put("nom", "Fredo");
            enfant2.put("mere", parentCarmella);
            enfant2.put("pere", parentVito);
            listeEnfants.add(enfant2);

            Map<String, Object> enfant3 = new NtroHashMap<>();
            enfant3.put("nom", "Michael");
            enfant3.put("mere", parentCarmella);
            enfant3.put("pere", parentVito);
            listeEnfants.add(enfant3);

            parentCarmella.put("enfants", listeEnfants);
            parentVito.put("enfants", listeEnfants);
        } else if (id.equals("ex03")) {
            Map<String, Object> precedent = this.racine;
            this.racine.put("i", 0);
            this.racine.put("suivant", precedent);

            for (int i = 1; i < 10; i++) {
                Map<String, Object> courant = new NtroHashMap<>();
                courant.put("i", i);
                precedent.put("suivant", courant);
                precedent = courant;
            }

            precedent.put("suivant", null);
        } else if (id.equals("ex04")) {
            Map<String, Object> precedent = this.racine;
            this.racine.put("i", 0);
            this.racine.put("precedent", null);

            for (int i = 1; i < 10; i++) {
                Map<String, Object> courant = new NtroHashMap<>();
                courant.put("i", i);
                courant.put("precedent", precedent);
                precedent.put("suivant", courant);
                precedent = courant;
            }

            precedent.put("suivant", null);
        } else if (id.equals("ex05")) {
            this.racine.put("i", 0);
            this.racine.put("moinsDeux", null);
            this.racine.put("plusUn", this.racine);

            Map<String, Object> avantPrecedent = null;
            Map<String, Object> precedent = this.racine;
            Map<String, Object> courant = null;

            for (int i = 1; i < 10; i++) {
                courant = new NtroHashMap<>();
                courant.put("i", i);

                if (i >= 2) {
                    courant.put("moinsDeux", avantPrecedent);
                } else {
                    courant.put("moinsDeux", null);
                }

                precedent.put("plusUn", courant);

                avantPrecedent = precedent;
                precedent = courant;
            }

            if (courant != null) {
                courant.put("plusUn", null);
            }
        } else if (id.equals("ex06")) {
            this.racine.put("i", 0);
            this.racine.put("moinsDeux", null);
            this.racine.put("plusUn", this.racine);

            Map<String, Object> avantPrecedent = null;
            Map<String, Object> precedent = this.racine;
            Map<String, Object> courant = null;

            for (int i = 1; i < 30; i++) {
                courant = new NtroHashMap<>();
                courant.put("i", i);

                if (i >= 2) {
                    courant.put("moinsDeux", avantPrecedent);
                } else {
                    courant.put("moinsDeux", null);
                }

                precedent.put("plusUn", courant);

                avantPrecedent = precedent;
                precedent = courant;
            }

            if (courant != null) {
                courant.put("plusUn", null);
            }
        }
    }
}
