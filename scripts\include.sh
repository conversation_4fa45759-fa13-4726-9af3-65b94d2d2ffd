# Copyright (C) (2022) (<PERSON><PERSON>) (<EMAIL>)
#
# This file is part of Ntro
#
# Ntro is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# Ntro is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with aquiletour.  If not, see <https://www.gnu.org/licenses/>

export GRADLE_OPTS="-Dfile.encoding=utf-8" 


ntroDependencies="core app_fx"
cardsDependencies="common common_procedure freesort_procedure shift_procedure validator_common validator_shift shift2_procedure validator_shift2 shift3_procedure validator_shift3 fibonacci_procedure"
ateliers_cards="atelier1_1 atelier1_3 atelier2_1_A atelier2_1_B atelier2_2_A atelier2_2_B atelier2_3"
ateliers3c6="atelier1_2_A atelier1_2_B atelier1_2_C atelier1_2_D atelier1_2_E atelier3_1_A atelier3_1_B atelier3_2_A atelier3_2_B atelier3_2_C atelier3_3_A atelier3_3_B atelier3_3_C atelier4_1 atelier4_2 atelier4_3_A atelier4_3_B atelier5_1 atelier5_2 atelier5_3_A atelier5_3_B"
ateliers3c6_avec_db="atelier1_2_A atelier1_2_B atelier1_2_C atelier1_2_D atelier1_2_E atelier3_1_A atelier3_1_B atelier3_2_A atelier3_2_B atelier3_2_C atelier3_3_A atelier3_3_B atelier3_3_C"
tous_les_ateliers="$ateliers_cards $ateliers3c6"
c6Dependencies="3c6_core 3c6_examples 3c6_java 3c6_nitrite 3c6_swing 3c6_tutoriels $ateliers3c6"

root_dir=$(dirname "$scripts_dir")

if uname | grep -q -i linux; then

    os="linux"

elif uname | grep -q -i mingw64; then

    os="windows"

else

    os="macos"

fi

home_dir="$HOME"

#uname=$(id | sed "s/uid=[0-9]\+(//" | sed "s/).*//")

#if [ "$os" = "windows" ]; then
    #home_dir="/c/Users/<USER>"
#else
    #home_dir="$HOME"
#fi

#project_cache_dir="$home_dir/.gradle_3c6"
#gradle_args=--project-cache-dir='"'$project_cache_dir'"'" --continue "
gradle_args=" --continue "

save_dir(){

    current_dir=$(pwd)
}

restore_dir(){

    cd "$current_dir"
}




