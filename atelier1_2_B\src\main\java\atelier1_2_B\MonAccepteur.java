package atelier1_2_B;

import atelier1_2_B.vehicules.Moto;
import atelier1_2_B.vehicules.Vehicule;

public class MonAccepteur implements Accepteur {
    @Override
    public boolean accepterSiDeuxRoues(Vehicule vehicule) {
        return vehicule.nombreDeRoues() == 2;
    }

    @Override
    public boolean accepterSiEconomique(Vehicule vehicule) {
        return vehicule.consommationLitresParKilometre() <= 6.0;
    }

    @Override
    public boolean accepterSiMoto(Vehicule vehicule) {
        return vehicule instanceof Moto;
    }
}
