# Java
*.class

# Gradle
.gradle
build
!gradle-wrapper.jar

# vim
*.swp

# emacs
*~

# Eclipse
.settings
.classpath
.project
.metadata
bin

# Idea
.idea
*.iml

# VScode
/.vscode/*
!/.vscode/settings.json
!/.vscode/extensions.json

# MAC_OS
.DS_Store

# Ntro 
/*/_storage/models/*.json
/*/_storage/models/*/*.json
/*/_storage/graphs
/*/_storage/options
/*/_storage/sessions
/*/_storage/tmp
/*/_storage/*.log
/*/_storage/*.html


# archives
*.7z
*.zip

# .db ne contenant pas d'info
atelier4_1/atelier4_1.db
atelier4_2/atelier4_2.db
atelier4_3_A/atelier4_3_A.db
atelier4_3_B/atelier4_3_B.db
atelier5_1/atelier5_1.db
atelier5_2/atelier5_2.db
atelier5_3_A/atelier5_3_A.db
atelier5_3_B/atelier5_3_B.db

# .html qu'on peut regénérer
atelier4_1/*.html
atelier4_2/*.html
atelier4_3_A/*.html
atelier4_3_B/*.html
atelier5_1/*.html
atelier5_2/*.html
atelier5_3_A/*.html
atelier5_3_B/*.html

# exceptions Ntro
!/atelier2_1_A/_storage/models/*/*.json
!/atelier2_1_B/_storage/models/*/*.json

# ignorer /_storage s'il existe par erreur
/_storage
