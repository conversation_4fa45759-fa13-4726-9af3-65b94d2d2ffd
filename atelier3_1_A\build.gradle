plugins {
    id 'local-java-plugin'
    id 'local-fx-plugin'
    id 'local-ntro-plugin'
}


dependencies {
    c6Dependencies.split(',').each {
        implementation files("../libs/${it}-${version}.jar")
    }

    ntroDependencies.split(',').each {
        implementation files("../libs/${it}-${version}.jar")
    }

    atelier3_1_ADependencies.split(',').each {
        implementation files("../libs/${it}-${version}.jar")
    }

    implementation group: 'org.dizitart', name: 'nitrite', version: '3.4.1'
    implementation 'com.google.code.gson:gson:2.8.5'
}


task(atelier3_1_A, dependsOn: 'classes', type: JavaExec) {
   main="atelier3_1_A.MonAtelier3_1_A"
   classpath = sourceSets.main.runtimeClasspath
   standardInput = System.in
   jvmArgs('-Dfile.encoding=UTF-8')
}
