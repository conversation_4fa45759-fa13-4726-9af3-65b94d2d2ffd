package atelier2_2_A;

import common.models.enums.Sorte;
import common.models.values.cards.Carte;
import shift3_procedure.models.Tableau;

public class MonTableau extends Tableau {
    @Override
    public void initialize(String id) {
        if(id.equals("ex01")){
            this.cartes = new MaCarte[3];
    
            this.cartes[0] = new MaCarte(1, Sorte.CARREAU);
            this.cartes[2] = new MaCarte(1, Sorte.COEUR);

            this.aDeplacer = 2;
            this.i = 1;

            this.memoireB = new MaCarte(1, Sorte.TREFLE);
        }else if(id.equals("ex02")){
            this.cartes = new MaCarte[4];
    
            this.cartes[0] = new MaCarte(1, Sorte.TREFLE);
            this.cartes[1] = new MaCarte(1, Sorte.PIQUE);
            this.cartes[3] = new MaCarte(1, Sorte.COEUR);

            this.aDeplacer = 0;
            this.i = 2;

            this.memoireA = new MaCarte(1, Sorte.CARREAU);
            this.memoireB = new MaCarte(1, Sorte.COEUR);
        }else if(id.equals("ex03")){
            this.cartes =  new MaCarte[2];

            this.cartes[0] = new MaCarte(1, Sorte.COEUR);

            this.aDeplacer = -1;
            this.i= 3;

            this.memoireA = new MaCarte(2, Sorte.COEUR);
            this.memoireB = new MaCarte(1, Sorte.PIQUE);
        }
    }    
}
