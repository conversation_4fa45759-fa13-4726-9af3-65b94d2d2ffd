rootProject.name = 'enonces3c6'

include 'atelier1_1'

include 'atelier1_2_A'
include 'atelier1_2_B'
include 'atelier1_2_C'
include 'atelier1_2_D'
include 'atelier1_2_E'

include 'atelier1_3'

include 'atelier2_1_A'
include 'atelier2_1_B'

include 'atelier2_2_A'
include 'atelier2_2_B'

include 'atelier2_3'

//include 'examen2_gr1'
//include 'examen2_gr2'

include 'atelier3_1_A'
include 'atelier3_1_B'

include 'atelier3_2_A'
include 'atelier3_2_B'
include 'atelier3_2_C'

include 'atelier3_3_A'
include 'atelier3_3_B'
include 'atelier3_3_C'

include 'atelier4_1'
include 'atelier4_2'
include 'atelier4_3_A'
include 'atelier4_3_B'

include 'atelier5_1'
include 'atelier5_2'
include 'atelier5_3_A'
include 'atelier5_3_B'

buildscript {

    repositories {
	    mavenCentral()
	    maven {url "https://plugins.gradle.org/m2/"}
    }

    dependencies {
        classpath 'org.openjfx:javafx-plugin:0.0.10'
    }
}
