package atelier2_2_B;

import ca.ntro.app.NtroAppFx;
import ca.ntro.cards.validator.Validator;
import validator_shift3.ValiderShift3;
import validator_shift3.models.Shift3Model;

public class Valider extends ValiderShift3 {
    public static void main(String[] args) {
        NtroAppFx.launch(args);
    }

    @Override
    protected void validateModels(Validator<Shift3Model> validator) {
        // ...
        validator.validateModel(MesDonneesJava.class, "ex01");
        validator.validateModel(MesDonneesJava.class, "ex02");
        validator.validateModel(MesDonneesJava.class, "ex03");
        validator.validateModel(MesDonneesJava.class, "ex04");
        validator.validateModel(MesDonneesJava.class, "ex05");
        validator.validateModel(MesDonneesJava.class, "ex06");
    }
}
