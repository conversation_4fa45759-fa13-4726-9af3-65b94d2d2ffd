package atelier2_3;

import ca.ntro.app.NtroAppFx;
import fibonacci_procedure.FibonacciProcedureApp;

public class Procedure extends FibonacciProcedureApp<MonCalculateur, MonFibonacci> {
    public static void main(String[] args) {
        NtroAppFx.launch(args);
    }

    @Override
    protected Class<MonCalculateur> classeCalculateur() {
        return MonCalculateur.class;
    }

    @Override
    protected Class<MonFibonacci> classeFibonacci() {
        return MonFibonacci.class;
    }
}
