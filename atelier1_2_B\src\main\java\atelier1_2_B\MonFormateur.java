package atelier1_2_B;

import atelier1_2_B.vehicules.Auto;
import atelier1_2_B.vehicules.Camion;
import atelier1_2_B.vehicules.Fourgonnette;
import atelier1_2_B.vehicules.Mobilette;
import atelier1_2_B.vehicules.Moto;
import atelier1_2_B.vehicules.Vehicule;

public class MonFormateur implements Formateur {
	@Override
	public String formater(Vehicule vehicule) {

		StringBuilder builder = new StringBuilder();

		if(vehicule instanceof Auto) {
			builder.append("Une auto");
		}else if(vehicule instanceof Camion) {
			builder.append("Un camion");
		}else if(vehicule instanceof Fourgonnette) {
			builder.append("Une fourgonnette");
		}else if(vehicule instanceof Moto) {
			builder.append("Une moto");
		}else if(vehicule instanceof Mobilette) {
			builder.append("Une mobilette");
		}

		builder.append(" est un véhicule à ");
		builder.append(vehicule.nombreDeRoues());
		builder.append(" roues. Sa consommation d'essence est ");
		builder.append(vehicule.consommationLitresParKilometre());
		builder.append(" litres par kilomètre.");

		return builder.toString();
	}
}
