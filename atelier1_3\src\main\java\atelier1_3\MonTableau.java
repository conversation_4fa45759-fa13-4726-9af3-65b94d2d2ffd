package atelier1_3;

import common.test_cases.execution.Execution;
import shift_procedure.models.Tableau;

public class MonTableau extends Tableau {
    @Override
    public void deplacerDecaler() {
        if (insererAuDebut) {
            insererAuDebut();
        } else {
            insererALaFin();
        }
    }

    private void insererALaFin() {
        this.memoireA = this.cartes[this.aDeplacer];
        Execution.ajouterEtape();

        this.cartes[this.aDeplacer] = null;

        Execution.ajouterEtape();

        this.i = this.aDeplacer;

        Execution.ajouterEtape();

        while (this.i < this.cartes.length - 1) {
            this.cartes[this.i] = this.cartes[this.i + 1];
            this.cartes[this.i + 1] = null;
            Execution.ajouterEtape();
            this.i++;
            Execution.ajouterEtape();

            if (this.i == this.cartes.length - 1) {
                this.cartes[this.i] = this.memoireA;
                this.memoireA = null;
                Execution.ajouterEtape();
            }
        }
    }

    private void insererAuDebut() {
        this.memoireA = this.cartes[this.aDeplacer];
        Execution.ajouterEtape();

        this.cartes[this.aDeplacer] = null;

        Execution.ajouterEtape();

        while (this.memoireA != null) {
            this.i++;

            Execution.ajouterEtape();

            this.memoireB = this.cartes[i];
            Execution.ajouterEtape();
            this.cartes[i] = this.memoireA;
            Execution.ajouterEtape();
            this.memoireA = this.memoireB;
            this.memoireB = null;
            Execution.ajouterEtape();
        }
    }
}
