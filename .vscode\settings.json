{"java.dependency.packagePresentation": "hierarchical", "java.jdt.ls.vmargs": "-Dfile.encoding=utf-8", "java.debug.settings.vmArgs": "-Dfile.encoding=utf-8", "java.compile.nullAnalysis.mode": "disabled", "java.import.gradle.offline.enabled": true, "java.inlayHints.parameterNames.enabled": "all", "[java]": {"editor.defaultFormatter": "redhat.java"}, "editor.inlayHints.enabled": "offUnlessPressed", "workbench.editor.closeOnFileDelete": true, "workbench.editor.languageDetection": false, "workbench.editor.empty.hint": "hidden", "explorer.compactFolders": false, "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "icon": "terminal-powershell", "args": ["-NoExit", "/c", "chcp 65001"]}, "Command Prompt": {"path": ["${env:windir}\\Sysnative\\cmd.exe", "${env:windir}\\System32\\cmd.exe"], "args": ["/K", "chcp 65001"], "icon": "terminal-cmd"}}, "files.exclude": {".vscode": true, ".git": true, ".gradle": true, "gradle": true, "gradlew": true, "gradlew.bat": true, "gradle.properties": true, "settings.gradle": true, "build.gradle": true, "**/.project": true, "**/.classpath": true, "**/.settings": true, "**/bin": true, "**/build": true, "**/.gitinclude": true, "**/_storage/options": true, "**/_storage/sessions": true, "buildSrc": true, "libs": true, ".gitignore": true, "*.zip": true, "scripts": true, "*.swp": true}}