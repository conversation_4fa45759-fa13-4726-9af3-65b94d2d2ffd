plugins {
    id 'local-java-plugin'
    id 'local-fx-plugin'
    id 'local-ntro-plugin'
}


dependencies {
    ntroDependencies.split(',').each {
        implementation files("../libs/${it}-${version}.jar")
    }

    c6Dependencies.split(',').each {
        implementation files("../libs/${it}-${version}.jar")
    }

    atelier4_1Dependencies.split(',').each {
        implementation files("../libs/${it}-${version}.jar")
    }

    implementation group: 'org.dizitart', name: 'nitrite', version: '3.4.1'
    implementation 'com.google.code.gson:gson:2.8.5'
    implementation 'guru.nidi:graphviz-java-all-j2v8:0.18.0'
}

task(atelier4_1, dependsOn: 'classes', type: JavaExec) {
   main= "atelier4_1.MonAtelier4_1"
   classpath = sourceSets.main.runtimeClasspath
   standardInput = System.in
   jvmArgs('-Dfile.encoding=UTF-8')
}
