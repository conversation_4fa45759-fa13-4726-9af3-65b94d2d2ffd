package atelier1_2_E;

public abstract class Vehicule implements <PERSON><PERSON><PERSON><PERSON>, Formateur {
    public Vehicule(double totalKilometres) {
        this.totalKilometres = totalKilometres;
    }

    private double totalKilometres = 0;
    
    protected abstract double consommationLitresParKilometre();

	protected abstract String nomVehicule();

	protected abstract boolean siNomFeminin();

	private double litresEssenceConsomes() {
		return totalKilometres * consommationLitresParKilometre();
	}

	@Override
	public String formater() {
		StringBuilder builder = new StringBuilder();

		if(siNomFeminin()) {
			builder.append("Ma ");
		}else {
			builder.append("Mon ");
		}
		builder.append(nomVehicule());
		builder.append(" a roulé ");
		builder.append(totalKilometres);
		builder.append(" kilomètres et consomé ");
		builder.append(litresEssenceConsomes());
		builder.append(" litres d'essence.");

		return builder.toString();
	}

    @Override
    public void rouler(double kilometres) {
        totalKilometres += kilometres;
    }

}
