package atelier2_2_A;

import ca.ntro.app.NtroAppFx;
import shift3_procedure.ProcedureDecaler;

public class Procedure extends ProcedureDecaler<MonTableau, MaCarte> {
    public static void main(String[] args) {
        NtroAppFx.launch(args);
    }

    protected Class<MonTableau> classeMonTableau() {
        return MonTableau.class;
    }

    protected Class<MaCarte> classeMaCarte() {
        return MaCarte.class;
    }
}
