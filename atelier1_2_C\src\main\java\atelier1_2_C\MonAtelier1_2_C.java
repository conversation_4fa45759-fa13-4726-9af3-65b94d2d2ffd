package atelier1_2_C;

public class MonAtelier1_2_C extends Atelier1_2_C {
    public static void main(String[] args) {
        new MonAtelier1_2_C().valider();
    } 

    @Override
    public Rouleur creerAuto() {
        return new Auto();
    }

    @Override
    public Rouleur creerCamion() {
        return new Camion();
    }

    @Override
    public Rouleur creerMobilette() {
        return new Mobilette();
    }

    @Override
    public Rouleur creerMoto() {
        return new Moto();
    }

}
