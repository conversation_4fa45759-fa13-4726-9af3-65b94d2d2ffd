plugins {
    id 'local-java-plugin'
    id 'local-fx-plugin'
    id 'local-ntro-plugin'
}


dependencies {
    c6Dependencies.split(',').each {
        implementation files("../libs/${it}-${version}.jar")
    }

    ntroDependencies.split(',').each {
        implementation files("../libs/${it}-${version}.jar")
    }

    atelier1_2_BDependencies.split(',').each {
        implementation files("../libs/${it}-${version}.jar")
    }

    implementation group: 'org.dizitart', name: 'nitrite', version: '3.4.1'
    implementation 'com.google.code.gson:gson:2.8.5'
}

task(atelier1_2_B, dependsOn: 'classes', type: JavaExec) {
   main="atelier1_2_B.MonAtelier1_2_B"
   classpath = sourceSets.main.runtimeClasspath
   standardInput = System.in
   jvmArgs('-Dfile.encoding=UTF-8')
}
