dependencies {

    implementation 'org.slf4j:slf4j-nop:1.7.30'
    //implementation 'org.slf4j:slf4j-simple:1.7.30'

    implementation 'guru.nidi:graphviz-java-all-j2v8:0.18.0'
    implementation 'org.java-websocket:Java-WebSocket:1.5.3'
    implementation 'org.mongodb:bson:4.9.1'

}

task saveJson {
    doLast {
        println("")
        println("")
        println("[INFO] saving $projectDir/_storage/models")
        copy {
            from "$projectDir/_storage/models"
            into "$projectDir/json/models"
        }

        println("[INFO] saving $projectDir/_storage/sessions")
        copy {
            from "$projectDir/_storage/sessions"
            into "$projectDir/json/sessions"
        }

        println("[INFO] saving $projectDir/_storage/options")
        copy {
            from "$projectDir/_storage/options"
            into "$projectDir/json/options"
        }

    }
}

task restoreJson {
    doLast {

        println("[INFO] restoring models")
        copy {
            from "$projectDir/json/models"
            into "$projectDir/_storage/models"
        }

        println("[INFO] restoring sessions")
        copy {
            from "$projectDir/json/sessions"
            into "$projectDir/_storage/sessions"
        }

        println("[INFO] restoring options")
        copy {
            from "$projectDir/json/options"
            into "$projectDir/_storage/options"
        }

    }
}

