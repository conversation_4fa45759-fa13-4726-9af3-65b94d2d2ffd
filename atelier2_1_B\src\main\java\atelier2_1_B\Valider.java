package atelier2_1_B;

import ca.ntro.app.NtroAppFx;
import ca.ntro.cards.validator.Validator;
import validator_shift2.ValiderShift;
import validator_shift2.models.Shift2Model;

public class Valider extends ValiderShift {
    public static void main(String[] args) {
        NtroAppFx.launch(args);
    }

    @Override
    protected void validateModels(Validator<Shift2Model> validator) {
        validator.validateModel(MesDonneesJson.class, "ex01");
        validator.validateModel(MesDonneesJson.class, "ex02");
        validator.validateModel(MesDonneesJson.class, "ex03");

        validator.validateModel(MonTableau.class, "ex01");
        validator.validateModel(MonTableau.class, "ex02");
        validator.validateModel(MonTableau.class, "ex03");
    }
}
