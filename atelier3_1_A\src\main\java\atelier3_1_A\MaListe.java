package atelier3_1_A;

public class MaListe implements Liste<MaListe> {
    private Comparable[] valeurs;

    public MaListe(Comparable[] valeursInitiales) {
        this.valeurs = valeursInitiales;
    }

    @Override
    public int compareTo(MaListe o) {
        return 0;
    }

    @Override
    public Comparable obtenirValeur(int indice) {
        return valeurs[indice];
    }

    @Override
    public void modifierValeur(int indice, Comparable valeur) {
        valeurs[indice] = valeur;
    }

    @Override
    public Comparable valeurMinimale() {
        Comparable min = valeurs[0];
        for (int i = 1; i < valeurs.length; i++) {
            if (valeurs[i].compareTo(min) < 0) {
                min = valeurs[i];
            }
        }
        return min;
    }
}
