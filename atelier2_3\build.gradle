plugins {
    id 'local-java-plugin'
    id 'local-fx-plugin'
    id 'local-ntro-plugin'
}


dependencies {
    ntroDependencies.split(',').each {
        implementation files("../libs/${it}-${version}.jar")
    }

    cardsDependencies.split(',').each {
        implementation files("../libs/${it}-${version}.jar")
    }

    atelier2_3Dependencies.split(',').each {
        implementation files("../libs/${it}-${version}.jar")
    }

    implementation group: 'org.mongodb', name: 'mongo-java-driver', version: '3.12.8'
    implementation 'guru.nidi:graphviz-java-all-j2v8:0.18.0'
}

task(atelier2_3, dependsOn: 'classes', type: JavaExec) {
   main= "atelier2_3.Procedure"
   classpath = sourceSets.main.runtimeClasspath
   standardInput = System.in
   jvmArgs('-Dfile.encoding=UTF-8')
}
